{"_format": "hh3-artifact-1", "contractName": "Coin", "sourceName": "contracts/Coin.sol", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "uint256", "name": "requested", "type": "uint256"}, {"internalType": "uint256", "name": "available", "type": "uint256"}], "name": "InsufficientBalance", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "from", "type": "address"}, {"indexed": false, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "<PERSON><PERSON>", "type": "event"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "balances", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "minter", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "send", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x6080604052348015600e575f5ffd5b505f80546001600160a01b031916331790556102df8061002d5f395ff3fe608060405234801561000f575f5ffd5b506004361061004a575f3560e01c8063075461721461004e57806327e235e31461007d57806340c10f19146100aa578063d0679d34146100bf575b5f5ffd5b5f54610060906001600160a01b031681565b6040516001600160a01b0390911681526020015b60405180910390f35b61009c61008b366004610221565b60016020525f908152604090205481565b604051908152602001610074565b6100bd6100b8366004610241565b6100d2565b005b6100bd6100cd366004610241565b610117565b5f546001600160a01b031633146100e7575f5ffd5b6001600160a01b0382165f908152600160205260408120805483929061010e90849061027d565b90915550505050565b335f9081526001602052604090205481111561016a57335f908152600160205260409081902054905163cf47918160e01b8152610161918391600401918252602082015260400190565b60405180910390fd5b335f9081526001602052604081208054839290610188908490610296565b90915550506001600160a01b0382165f90815260016020526040812080548392906101b490849061027d565b9091555050604080513381526001600160a01b03841660208201529081018290527f3990db2d31862302a685e8086b5755072a6e2b5b780af1ee81ece35ee3cd33459060600160405180910390a15050565b80356001600160a01b038116811461021c575f5ffd5b919050565b5f60208284031215610231575f5ffd5b61023a82610206565b9392505050565b5f5f60408385031215610252575f5ffd5b61025b83610206565b946020939093013593505050565b634e487b7160e01b5f52601160045260245ffd5b8082018082111561029057610290610269565b92915050565b818103818111156102905761029061026956fea26469706673582212201d1e3dc4d0da84fe280f9ca1119e698aaef121d8b46f4b250b6400c20b73370264736f6c634300081c0033", "deployedBytecode": "0x608060405234801561000f575f5ffd5b506004361061004a575f3560e01c8063075461721461004e57806327e235e31461007d57806340c10f19146100aa578063d0679d34146100bf575b5f5ffd5b5f54610060906001600160a01b031681565b6040516001600160a01b0390911681526020015b60405180910390f35b61009c61008b366004610221565b60016020525f908152604090205481565b604051908152602001610074565b6100bd6100b8366004610241565b6100d2565b005b6100bd6100cd366004610241565b610117565b5f546001600160a01b031633146100e7575f5ffd5b6001600160a01b0382165f908152600160205260408120805483929061010e90849061027d565b90915550505050565b335f9081526001602052604090205481111561016a57335f908152600160205260409081902054905163cf47918160e01b8152610161918391600401918252602082015260400190565b60405180910390fd5b335f9081526001602052604081208054839290610188908490610296565b90915550506001600160a01b0382165f90815260016020526040812080548392906101b490849061027d565b9091555050604080513381526001600160a01b03841660208201529081018290527f3990db2d31862302a685e8086b5755072a6e2b5b780af1ee81ece35ee3cd33459060600160405180910390a15050565b80356001600160a01b038116811461021c575f5ffd5b919050565b5f60208284031215610231575f5ffd5b61023a82610206565b9392505050565b5f5f60408385031215610252575f5ffd5b61025b83610206565b946020939093013593505050565b634e487b7160e01b5f52601160045260245ffd5b8082018082111561029057610290610269565b92915050565b818103818111156102905761029061026956fea26469706673582212201d1e3dc4d0da84fe280f9ca1119e698aaef121d8b46f4b250b6400c20b73370264736f6c634300081c0033", "linkReferences": {}, "deployedLinkReferences": {}, "immutableReferences": {}, "inputSourceName": "project/contracts/Coin.sol", "buildInfoId": "solc-0_8_28-c6e6768ba3a4e9b77b4b5fd278a64b443680bff6"}