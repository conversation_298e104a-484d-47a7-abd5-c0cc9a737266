// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

import "@aave/core-v3/contracts/flashloan/base/FlashLoanSimpleReceiverBase.sol";
import "@uniswap/v3-periphery/contracts/interfaces/ISwapRouter.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";

contract PrintMoney is FlashLoanSimpleReceiverBase {
    using SafeERC20 for IERC20;

    // ====== Constants (Polygon Mainnet) ======
    address public constant USDC = 0x2791Bca1f2de4661ED88A30C99A7a9449Aa84174;
    address public constant WPOL = 0x0d500B1d8E8eF31E21C99d1Db9A6444d3ADf1270;

    // Uniswap v3 & QuickSwap v3 routers
    ISwapRouter public constant UNI_V3 =
        ISwapRouter(0xE592427A0AEce92De3Edee1F18E0157C05861564);
    ISwapRouter public constant QUICK_V3 =
        ISwapRouter(0xf5b509bB0909a69B1c207E495f687a596C168E12);

    // 固定费用档位：0.05% (500)
    uint24 private constant FEE_500 = 500;

    address public owner;

    // ====== Events ======
    event Executed(
        address indexed caller,
        uint256 amountUSDC,
        bool orderUniToQuick
    );
    event Swapped(
        address indexed router,
        address tokenIn,
        address tokenOut,
        uint256 amountIn,
        uint256 amountOut
    );
    event Profit(address indexed to, uint256 profitUSDC);
    event Withdraw(address indexed to, address token, uint256 amount);

    // ====== Constructor ======
    constructor(
        IPoolAddressesProvider provider
    ) FlashLoanSimpleReceiverBase(provider) {
        owner = msg.sender;

        // 一次性无限授权，避免每次 swap 再 approve 浪费 gas
        IERC20(USDC).safeApprove(address(UNI_V3), type(uint256).max);
        IERC20(USDC).safeApprove(address(QUICK_V3), type(uint256).max);
        IERC20(WPOL).safeApprove(address(UNI_V3), type(uint256).max);
        IERC20(WPOL).safeApprove(address(QUICK_V3), type(uint256).max);
    }

    modifier onlyOwner() {
        require(msg.sender == owner, "not owner");
        _;
    }

    // ====== External entry ======
    /**
     * @param amountUSDC  闪电贷借入 USDC 数量
     * @param order       交易顺序：true=UNI(USDC->WPOL)->QUICK(WPOL->USDC)，false=QUICK->UNI
     * @param minOut1     第一次 swap 的最小可接受输出（WPOL）
     * @param minOut2     第二次 swap 的最小可接受输出（USDC）
     * @param deadline    两次 swap 的共同截止时间（区块时间戳，建议 now+30~60s）
     */
    function execute(
        uint256 amountUSDC,
        bool order,
        uint256 minOut1,
        uint256 minOut2,
        uint256 deadline
    ) external onlyOwner {
        require(amountUSDC > 0, "amount=0");
        require(deadline >= block.timestamp, "deadline expired");
        // 将参数打包带入回调，避免存储读写
        bytes memory params = abi.encode(order, minOut1, minOut2, deadline);
        emit Executed(msg.sender, amountUSDC, order);

        POOL.flashLoanSimple(address(this), USDC, amountUSDC, params, 0);
    }

    // ====== Aave Callback ======
    function executeOperation(
        address asset,
        uint256 amount,
        uint256 premium,
        address /* initiator */,
        bytes calldata params
    ) external override returns (bool) {
        require(asset == USDC, "asset!=USDC");

        (bool order, uint256 minOut1, uint256 minOut2, uint256 deadline) = abi
            .decode(params, (bool, uint256, uint256, uint256));

        uint256 usdcBack;
        if (order) {
            // 顺序 1: UNI (USDC->WPOL) -> QUICK (WPOL->USDC)
            uint256 wpolOut = _swapExactIn(
                UNI_V3,
                USDC,
                WPOL,
                amount,
                minOut1,
                deadline
            );
            emit Swapped(address(UNI_V3), USDC, WPOL, amount, wpolOut);

            usdcBack = _swapExactIn(
                QUICK_V3,
                WPOL,
                USDC,
                wpolOut,
                minOut2,
                deadline
            );
            emit Swapped(address(QUICK_V3), WPOL, USDC, wpolOut, usdcBack);
        } else {
            // 顺序 2: QUICK (USDC->WPOL) -> UNI (WPOL->USDC)
            uint256 wpolOut = _swapExactIn(
                QUICK_V3,
                USDC,
                WPOL,
                amount,
                minOut1,
                deadline
            );
            emit Swapped(address(QUICK_V3), USDC, WPOL, amount, wpolOut);

            usdcBack = _swapExactIn(
                UNI_V3,
                WPOL,
                USDC,
                wpolOut,
                minOut2,
                deadline
            );
            emit Swapped(address(UNI_V3), WPOL, USDC, wpolOut, usdcBack);
        }

        // 计算应还款
        uint256 repay = amount + premium;
        require(usdcBack >= repay, "no profit");

        // 先批准 Aave POOL 扣款归还闪电贷
        IERC20(USDC).safeApprove(address(POOL), 0); // 一些代币要求先清零
        IERC20(USDC).safeApprove(address(POOL), repay);

        // 将利润直接转给 owner，避免留在合约里
        uint256 profit = usdcBack - repay;
        if (profit > 0) {
            IERC20(USDC).safeTransfer(owner, profit);
            emit Profit(owner, profit);
        }
        return true;
    }

    // ====== Internal swap helper (fee 固定为 500) ======
    function _swapExactIn(
        ISwapRouter router,
        address tokenIn,
        address tokenOut,
        uint256 amountIn,
        uint256 amountOutMin,
        uint256 deadline
    ) internal returns (uint256 amountOut) {
        require(amountIn > 0, "amountIn=0");
        require(deadline >= block.timestamp, "deadline expired");

        ISwapRouter.ExactInputSingleParams memory p = ISwapRouter
            .ExactInputSingleParams({
                tokenIn: tokenIn,
                tokenOut: tokenOut,
                fee: FEE_500,
                recipient: address(this),
                deadline: deadline,
                amountIn: amountIn,
                amountOutMinimum: amountOutMin, // 滑点保护由 off-chain 计算后传入
                sqrtPriceLimitX96: 0 // 不限制价格区间
            });

        amountOut = router.exactInputSingle(p);
    }

    // ====== Admin ======
    function withdraw(address token) external onlyOwner {
        uint256 bal = IERC20(token).balanceOf(address(this));
        IERC20(token).safeTransfer(owner, bal);
        emit Withdraw(owner, token, bal);
    }

    function withdrawNative() external onlyOwner {
        uint256 bal = address(this).balance;
        (bool ok, ) = owner.call{value: bal}("");
        require(ok, "withdrawNative failed");
        emit Withdraw(owner, address(0), bal);
    }

    function transferOwnership(address newOwner) external onlyOwner {
        require(newOwner != address(0), "zero addr");
        owner = newOwner;
    }

    // 接收原生 POL（一般不使用，但保底不拒收）
    receive() external payable {}
}
