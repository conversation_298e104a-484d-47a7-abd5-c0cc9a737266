{"_format": "hh3-sol-build-info-1", "id": "solc-0_8_28-c6e6768ba3a4e9b77b4b5fd278a64b443680bff6", "solcVersion": "0.8.28", "solcLongVersion": "0.8.28+commit.7893614a", "userSourceNameMap": {"contracts/Coin.sol": "project/contracts/Coin.sol"}, "input": {"language": "Solidity", "settings": {"evmVersion": "cancun", "optimizer": {"enabled": true, "runs": 200}, "outputSelection": {"*": {"": ["ast"], "*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"]}}, "remappings": []}, "sources": {"project/contracts/Coin.sol": {"content": "// SPDX-License-Identifier: GPL-3.0\r\npragma solidity ^0.8.28;\r\n\r\ncontract Coin {\r\n    // The keyword \"public\" makes variables\r\n    // accessible from other contracts\r\n    address public minter;\r\n    mapping(address => uint) public balances;\r\n    // Events allow clients to react to specific\r\n    // contract changes you declare\r\n    event Sent(address from, address to, uint amount);\r\n    // Constructor code is only run when the contract\r\n    // is created\r\n    constructor() {\r\n        minter = msg.sender;\r\n    }\r\n    // Sends an amount of newly created coins to an address\r\n    // Can only be called by the contract creator\r\n    function mint(address receiver, uint amount) public {\r\n        require(msg.sender == minter);\r\n        balances[receiver] += amount;\r\n    }\r\n    // Errors allow you to provide information about\r\n    // why an operation failed. They are returned\r\n    // to the caller of the function.\r\n    error InsufficientBalance(uint requested, uint available);\r\n    // Sends an amount of existing coins\r\n    // from any caller to an address\r\n    function send(address receiver, uint amount) public {\r\n        if (amount > balances[msg.sender])\r\n            revert InsufficientBalance({\r\n                requested: amount,\r\n                available: balances[msg.sender]\r\n            });\r\n        balances[msg.sender] -= amount;\r\n        balances[receiver] += amount;\r\n        emit Sent(msg.sender, receiver, amount);\r\n    }\r\n}\r\n"}}}}