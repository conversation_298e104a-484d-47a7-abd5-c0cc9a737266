import { network } from "hardhat";

async function main() {
    const { viem } = await network.connect({
        network: "amoy",
        chainType: "l1",
    });

    // 2. 取钱包客户端（hardhat.config.ts 中配置的 AMOY_PRIVATE_KEY）
    const [deployer] = await viem.getWalletClients();
    console.log("Deploying with account:", deployer.account.address);

    // 3. 部署合约
    const coin = await viem.deployContract("Coin");

    // 4. 等待链上确认
    const publicClient = await viem.getPublicClient();
    const receipt = await publicClient.waitForTransactionReceipt({
        hash: coin.deployTransaction,
    });

    console.log("Coin deployed to:", coin.address);
    console.log("Block number:", receipt.blockNumber);
}

main().catch((error) => {
    console.error(error);
    process.exitCode = 1;
});
