## Overview

Below is a practical, end-to-end guide to your Hardhat 3 project that explains the current scaffold, what each file does, and a complete development workflow covering contract development, testing, deployment (local/testnet/mainnet), MetaMask integration, and live-network testing. It includes concrete commands, examples, best practices, and troubleshooting tips.

---

## Project Analysis

### 1) Project structure at a glance

- contracts/
  - Counter.sol — simple example contract with state, events, and functions
  - Counter.t.sol — a Foundry test (not used by Hardhat tests)
- ignition/modules/
  - Counter.ts — Ignition module for deploying Counter and calling incBy
- scripts/
  - send-op-tx.ts — example script that uses an OP chain type (Optimism-like)
- test/
  - Counter.ts — example Hardhat 3 test using node:test and viem assertions
- hardhat.config.ts — Hardhat configuration (solidity profiles, networks)
- package.json — dev dependencies and ESM module type
- tsconfig.json — TypeScript setup
- artifacts/, cache/, node_modules/ — build output, cache, dependencies

### 2) Core files explained

- hardhat.config.ts
  - Uses @nomicfoundation/hardhat-toolbox-viem plugin for viem-native workflows in Hardhat 3.
  - Defines Solidity profiles (default and production) and multiple networks, including in-process EDR simulated networks and Sepolia via HTTP RPC.
  - Uses configVariable("ENV_NAME") to safely read environment variables.

  Example excerpt:
````ts path=hardhat.config.ts mode=EXCERPT
  solidity: {
    profiles: {
      default: { version: "0.8.28" },
      production: {
        version: "0.8.28",
        settings: { optimizer: { enabled: true, runs: 200 } },
      },
    },
  }
````

- package.json
  - Hardhat 3, viem, typescript, Ignition 3; project is ESM ("type": "module").

````json path=package.json mode=EXCERPT
  {
    "devDependencies": {
      "@nomicfoundation/hardhat-ignition": "^3.0.0",
      "@nomicfoundation/hardhat-toolbox-viem": "^5.0.0",
      "hardhat": "^3.0.1",
      "typescript": "~5.8.0",
      "viem": "^2.35.1"
    },
    "type": "module"
  }
````

- contracts/
  - Counter.sol: maintains a public x, emits Increment events on inc/incBy, rejects incBy(0).

````solidity path=contracts/Counter.sol mode=EXCERPT
  contract Counter {
    uint public x;
    event Increment(uint by);
    function inc() public { x++; emit Increment(1); }
  }
````

- test/
  - Counter.ts: uses node:test + viem to deploy/read/call and assert events.

````ts path=test/Counter.ts mode=EXCERPT
  import { describe, it } from "node:test";
  import { network } from "hardhat";
  describe("Counter", async function () {
    const { viem } = await network.connect();
    const publicClient = await viem.getPublicClient();
  });
````

- scripts/
  - send-op-tx.ts: connects to "hardhatOp" EDR-simulated OP chainType and sends a tx.

````ts path=scripts/send-op-tx.ts mode=EXCERPT
  const { viem } = await network.connect({ network: "hardhatOp", chainType: "op" });
  const publicClient = await viem.getPublicClient();
  const [senderClient] = await viem.getWalletClients();
````

- ignition/modules/
  - Counter.ts: Ignition deployment module for Counter; makes a post-deploy call.

````ts path=ignition/modules/Counter.ts mode=EXCERPT
  export default buildModule("CounterModule", (m) => {
    const counter = m.contract("Counter");
    m.call(counter, "incBy", [5n]);
    return { counter };
  });
````

- contracts/Counter.t.sol (Foundry)
  - A Forge test, not executed by Hardhat’s test runner. Keep it if you also use Foundry; otherwise it’s optional.

````solidity path=contracts/Counter.t.sol mode=EXCERPT
  import {Test} from "forge-std/Test.sol";
  contract CounterTest is Test {
    function setUp() public { /* ... */ }
  }
````

### 3) Existing smart contracts and their purposes

- Counter
  - Maintains a counter (x).
  - inc() increments by 1; incBy(by) increments by a positive amount; emits Increment(by).

---

## Complete Development Workflow Guide

### Prerequisites and Environment Setup

- Node.js: Use LTS (v20+) recommended
- Install dependencies:
  - npm ci (if package-lock.json present) or npm install
- Editor: VS Code + Solidity and TypeScript extensions recommended
- Optional: Direnv or .env for environment variables (SEPOLIA_RPC_URL, SEPOLIA_PRIVATE_KEY)

Command:
- npm install

To compile:
- npx hardhat compile
- With optimizer profile: npx hardhat compile --profile production

To run tests:
- npx hardhat test

Tip: Hardhat 3 uses EDR under the hood for fast, deterministic local execution.

---

### 1) Contract Development (from scratch)

Steps:
1) Create a new contract in contracts/MyToken.sol
2) Write Solidity 0.8.28 code (matches config) and include events, require checks, and NatSpec
3) Compile locally and fix warnings
4) Add tests in test/MyToken.ts using viem + node:test

Minimal example (ERC20-like pseudo, illustrative):
- Create file contracts/MyToken.sol
- Then run: npx hardhat compile

Best practices:
- Use solidity ^0.8.28 to match config
- Add events for key state changes
- Validate inputs with require
- Prefer immutable for constructor-set constants
- Use custom errors instead of string reverts for lower gas

Compilation profiles:
- Default: quick local dev
- Production: optimizer enabled, 200 runs
  - npx hardhat compile --profile production

---

### 2) Local Testing

Write tests in TypeScript with node:test and viem. You can follow the Counter example to:
- Deploy contract via viem.deployContract("ContractName")
- Call view functions via contract.read
- Send transactions via contract.write
- Assert events with viem.assertions

Run:
- npx hardhat test
- npx hardhat test test/Counter.ts

Tips:
- Use publicClient.getContractEvents to aggregate and verify emitted events
- For gas checks, you can use publicClient.estimateGas where relevant
- Keep tests deterministic and self-contained

---

### 3) Local Deployment

You have two good options:

Option A: Hardhat Ignition (recommended)
- Use the provided Ignition module (ignition/modules/Counter.ts) or create your own.
- Deploy to an in-process EDR network or to a JSON-RPC node.

Local in-process (no node server required):
- npx hardhat ignition deploy ignition/modules/Counter.ts --network hardhatMainnet

If you prefer to run a local JSON-RPC node:
- npx hardhat node
- In another terminal, deploy:
  - npx hardhat ignition deploy ignition/modules/Counter.ts --network localhost

Option B: Custom script with network.connect (useful for scripts or special flows)
- Example: scripts/send-op-tx.ts shows connecting to an OP-like chain type.
- To run:
  - npx hardhat run scripts/send-op-tx.ts

Notes:
- Your script can connect programmatically using network.connect() and then viem methods, without specifying --network.
- If you want CLI-selected network, avoid network.connect in the script and pass --network on the CLI.

---

### 4) Network Deployment (Sepolia, Goerli, Mainnet)

Prepare environment:
- Set env variables for networks you’ll use (as referenced by hardhat.config.ts):
  - SEPOLIA_RPC_URL
  - SEPOLIA_PRIVATE_KEY (funded account for deployment)

Windows PowerShell example:
- $env:SEPOLIA_RPC_URL="https://sepolia.infura.io/v3/<your-key>"
- $env:SEPOLIA_PRIVATE_KEY="0xabc...yourprivatekey..."

Check config (excerpt):
````ts path=hardhat.config.ts mode=EXCERPT
sepolia: {
  type: "http",
  chainType: "l1",
  url: configVariable("SEPOLIA_RPC_URL"),
  accounts: [configVariable("SEPOLIA_PRIVATE_KEY")],
},
````

Deploy with Ignition:
- npx hardhat ignition deploy ignition/modules/Counter.ts --network sepolia

Alternative: Script-based:
- If using a script that doesn’t call network.connect, run:
  - npx hardhat run scripts/deploy.ts --network sepolia

Mainnet:
- Add mainnet config similar to sepolia with MAINNET_RPC_URL and MAINNET_PRIVATE_KEY (be careful!)
- Fund the deployer account and consider gas price settings
- Use production compile: npx hardhat compile --profile production

Verification (optional):
- Install the verify plugin if desired: @nomicfoundation/hardhat-verify
- Configure Etherscan API key, then run:
  - npx hardhat verify --network sepolia <deployedAddress> <constructorArgs...>

---

### 5) MetaMask Integration

Local development
- If you need MetaMask to talk to your contracts locally, run a local JSON-RPC:
  - npx hardhat node
- Add a custom network in MetaMask:
  - Network Name: Hardhat Local
  - RPC URL: http://127.0.0.1:8545
  - Chain ID: 31337 (default)
  - Currency Symbol: ETH
- Import an account:
  - Hardhat node prints funded accounts and their private keys; import one into MetaMask for testing
  - DO NOT reuse these keys elsewhere

Testnets (Sepolia)
- Add Sepolia in MetaMask:
  - Network: Sepolia
  - RPC: your provider’s Sepolia RPC URL
  - Chain ID: ********
- Fund the deployer and tester accounts via faucet; confirm balances in MetaMask
- Interact with deployed contracts using:
  - Etherscan write/read interface (contract must be verified)
  - A simple front-end or wagmi/viem scripts

Optimism-like chain (OP)
- For live OP testnets (e.g., Optimism Sepolia), configure RPC and chain ID in MetaMask
- If you need to experiment with OP behavior locally, use your send-op-tx.ts script for in-process runs; for MetaMask, use an actual OP testnet node

---

### 6) Network Testing (Live environments)

Ways to test:
- Etherscan/Blockscout
  - Once verified, use “Read Contract” and “Write Contract” tabs
- Viem scripts
  - Use publicClient and walletClient connected to the live RPC to call/read your contract
- Hardhat console (optional)
  - npx hardhat console --network sepolia
  - Interact via viem plugin APIs (depends on current Hardhat 3 console status and toolbox support)
- Foundry cast (optional)
  - If you have Foundry installed: cast call, cast send, etc.

Sanity checklist:
- Confirm correct chain ID
- Confirm account has enough ETH for gas
- Prefer smaller, incremental calls first
- Watch events and receipts to verify behavior

---

## Additional How-Tos and Examples

### Example: Add a new Ignition module

Create ignition/modules/MyToken.ts:
- Example pattern:
  - const token = m.contract("MyToken", [arg1, arg2])
  - m.call(token, "transferOwnership", [owner])

Deploy:
- npx hardhat ignition deploy ignition/modules/MyToken.ts --network sepolia

### Example: Minimal deploy script (viem + Hardhat runtime)

A minimal script (scripts/deploy.ts) could:
- Connect: const { viem } = await network.connect()
- Deploy: const my = await viem.deployContract("MyToken", [args...])
- Log address: console.log(my.address)

Run:
- npx hardhat run scripts/deploy.ts --network sepolia

---

## Security and Gas Best Practices

- Use solidity ^0.8.28 and keep compiler/profiles aligned
- Use errors instead of long revert strings for gas efficiency
- Add access controls (onlyOwner, roles) for privileged functions
- Avoid reentrancy with checks-effects-interactions or nonReentrant guards
- Prefer immutable and constant where appropriate
- Be careful with external calls and trusted assumptions (use interfaces)
- Emit events for major state changes
- For production builds:
  - npx hardhat compile --profile production
- Consider formal reviews and fuzz/invariant tests (e.g., Foundry test patterns)
- Use realistic gas estimation and provide buffers on mainnet

---

## Troubleshooting Tips

- Missing environment variables
  - Error: “Variable SEPOLIA_RPC_URL is not set”
  - Fix: Set the environment variable before running Hardhat

- ESM/TypeScript issues
  - Project is ESM ("type":"module")
  - Keep imports ESM-friendly; avoid require()
  - Ensure tsconfig matches Hardhat’s expectations

- Chain ID / MetaMask mismatch
  - If MetaMask shows “Wrong network”, ensure the network RPC and Chain ID are correct

- Insufficient funds / gas
  - Check balances via RPC or faucet
  - Use publicClient.estimateGas and provide adequate gas price

- Contract not found when deploying
  - Compile first: npx hardhat compile
  - Ensure contract name in deploy script/module matches the file’s contract name

- Nonce too low / Transaction replacement
  - Wait for pending txs to confirm
  - Reset account in MetaMask (advanced) or increment nonce carefully

---

## Suggested Next Steps for Your Project

- Run tests locally:
  - npx hardhat test
- Deploy Counter via Ignition locally:
  - npx hardhat ignition deploy ignition/modules/Counter.ts --network hardhatMainnet
- Try the OP example script:
  - npx hardhat run scripts/send-op-tx.ts
- Configure Sepolia environment variables and deploy:
  - npx hardhat ignition deploy ignition/modules/Counter.ts --network sepolia
- Interact with deployed contract:
  - Use a small viem script or Etherscan’s interface

If you want, I can generate:
- A sample MyToken.sol
- A matching Ignition module
- A test file using viem
- A verification setup for Etherscan

Just let me know the token parameters or the contract idea you want to build next.
