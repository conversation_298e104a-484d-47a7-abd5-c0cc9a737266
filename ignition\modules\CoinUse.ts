import { buildModule } from "@nomicfoundation/hardhat-ignition/modules";

export default buildModule("CoinUseModule", (m) => {
    const coin = m.contractAt(
        "Coin",
        "0x2F9DBb0CD4f821C2842bCfeB3231e4eBaD0bad2e"
    );

    m.call(coin, "mint", ["0xd83313C53e41fbdC02965dEF3C3b0CE61d24E371", 1000n]);

    m.call(coin, "send", ["0xB23d9Af50ff1a269Ec0a3De6FefdE0B4627a8338", 500n]);

    return { coin };
});
